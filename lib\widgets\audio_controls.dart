import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/audio_service.dart';
import '../theme/app_theme.dart';

class AudioControls extends StatelessWidget {
  const AudioControls({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<AudioService>(
      builder: (context, audioService, _) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Titre de la section
            Padding(
              padding: const EdgeInsets.only(left: 16, bottom: 8),
              child: Text(
                'AUDIO SETTINGS',
                style: TextStyle(
                  color: AppTheme.neonBlue,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                  letterSpacing: 1.2,
                ),
              ),
            ),
            
            // Contrôle de la musique
            _buildSwitchTile(
              context,
              'Music',
              audioService.isMusicEnabled,
              Icons.music_note,
              AppTheme.neonPurple,
              () => audioService.toggleMusic(),
            ),
            
            // Volume de la musique
            if (audioService.isMusicEnabled)
              _buildSliderTile(
                context,
                'Music Volume',
                audioService.musicVolume,
                Icons.volume_up,
                AppTheme.neonPurple,
                (value) => audioService.setMusicVolume(value),
              ),
            
            // Contrôle des effets sonores
            _buildSwitchTile(
              context,
              'Sound Effects',
              audioService.isSoundEffectsEnabled,
              Icons.surround_sound,
              AppTheme.neonGreen,
              () => audioService.toggleSoundEffects(),
            ),
            
            // Volume des effets sonores
            if (audioService.isSoundEffectsEnabled)
              _buildSliderTile(
                context,
                'Effects Volume',
                audioService.effectsVolume,
                Icons.volume_up,
                AppTheme.neonGreen,
                (value) => audioService.setEffectsVolume(value),
              ),
          ],
        );
      },
    );
  }
  
  Widget _buildSwitchTile(
    BuildContext context,
    String title,
    bool value,
    IconData icon,
    Color color,
    VoidCallback onChanged,
  ) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: AppTheme.darkBackgroundSecondary,
        boxShadow: value ? AppTheme.getNeonGlow(color, intensity: 0.3) : null,
      ),
      child: Row(
        children: [
          Icon(icon, color: value ? color : Colors.grey),
          const SizedBox(width: 16),
          Text(
            title,
            style: TextStyle(
              color: value ? Colors.white : Colors.grey,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          Switch(
            value: value,
            onChanged: (_) => onChanged(),
            activeColor: color,
            activeTrackColor: color.withOpacity(0.3),
          ),
        ],
      ),
    );
  }
  
  Widget _buildSliderTile(
    BuildContext context,
    String title,
    double value,
    IconData icon,
    Color color,
    Function(double) onChanged,
  ) {
    return Container(
      margin: const EdgeInsets.only(left: 32, right: 16, bottom: 16),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: AppTheme.darkBackgroundSecondary.withOpacity(0.7),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 16),
              const SizedBox(width: 8),
              Text(
                title,
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                ),
              ),
            ],
          ),
          Slider(
            value: value,
            onChanged: onChanged,
            activeColor: color,
            inactiveColor: color.withOpacity(0.2),
            min: 0.0,
            max: 1.0,
          ),
        ],
      ),
    );
  }
}
