import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../theme/app_theme.dart';

class CodeDisplay extends StatelessWidget {
  final String currentCode;
  final int codeLength;
  final bool showError;

  const CodeDisplay({
    super.key,
    required this.currentCode,
    required this.codeLength,
    this.showError = false,
  });

  @override
  Widget build(BuildContext context) {
    // Calculer les dimensions en fonction de la taille de l'écran
    final screenWidth = MediaQuery.of(context).size.width;
    final digitWidth =
        screenWidth < 360 ? 45.0 : (screenWidth < 400 ? 50.0 : 60.0);
    final digitHeight =
        screenWidth < 360 ? 60.0 : (screenWidth < 400 ? 70.0 : 80.0);
    final digitMargin =
        screenWidth < 360 ? 4.0 : (screenWidth < 400 ? 6.0 : 8.0);
    final fontSize =
        screenWidth < 360 ? 28.0 : (screenWidth < 400 ? 32.0 : 36.0);

    return Container(
          margin: EdgeInsets.symmetric(
            vertical: screenWidth < 360 ? 16.0 : 24.0,
            horizontal: 16.0,
          ),
          padding: EdgeInsets.symmetric(
            vertical: screenWidth < 360 ? 12.0 : 16.0,
            horizontal: screenWidth < 360 ? 16.0 : 24.0,
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: const LinearGradient(
              colors: [Color(0xFF1A1A1A), Color(0xFF2A2A2A)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            boxShadow:
                showError
                    ? AppTheme.getNeonGlow(AppTheme.neonPink, intensity: 0.7)
                    : AppTheme.getNeonGlow(AppTheme.neonBlue, intensity: 0.3),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(codeLength, (index) {
              final hasDigit = index < currentCode.length;
              final digit = hasDigit ? currentCode[index] : '';

              return Container(
                    width: digitWidth,
                    height: digitHeight,
                    margin: EdgeInsets.symmetric(horizontal: digitMargin),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      gradient:
                          hasDigit
                              ? AppTheme.primaryGradient
                              : const LinearGradient(
                                colors: [Color(0xFF2A2A2A), Color(0xFF3A3A3A)],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                      boxShadow:
                          hasDigit
                              ? AppTheme.getNeonGlow(
                                AppTheme.neonBlue,
                                intensity: 0.5,
                              )
                              : null,
                    ),
                    child: Center(
                      child: Text(
                        digit,
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: fontSize,
                        ),
                      ),
                    ),
                  )
                  .animate(target: hasDigit ? 1 : 0)
                  .scale(
                    begin: const Offset(0.9, 0.9),
                    end: const Offset(1, 1),
                    duration: const Duration(milliseconds: 200),
                    curve: Curves.easeOutQuad,
                  );
            }),
          ),
        )
        .animate(target: showError ? 1 : 0)
        .shakeX(amount: 10, hz: 4, duration: const Duration(milliseconds: 500));
  }
}
