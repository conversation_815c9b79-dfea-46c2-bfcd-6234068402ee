import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'game_model.dart';

class GameProvider extends ChangeNotifier {
  GameModel _gameModel;
  int _gamesPlayed = 0;
  int _gamesWon = 0;
  int _bestAttempts = 999;
  GameDifficulty _difficulty = GameDifficulty.medium;

  GameProvider() : _gameModel = GameDifficulty.medium.getGameModel() {
    _loadStats();
  }

  // Getters
  GameModel get gameModel => _gameModel;
  int get gamesPlayed => _gamesPlayed;
  int get gamesWon => _gamesWon;
  int get bestAttempts => _bestAttempts;
  GameDifficulty get difficulty => _difficulty;
  List<Attempt> get attempts => _gameModel.attempts;
  GameStatus get status => _gameModel.status;
  String get secretCode => _gameModel.secretCode;
  int get hintsUsed => _gameModel.hintsUsed;
  int get maxHints => _gameModel.maxHints;
  bool get hasHintsAvailable => _gameModel.hasHintsAvailable();

  // Méthodes pour le jeu
  Attempt makeGuess(String guess) {
    final attempt = _gameModel.makeGuess(guess);

    // Mettre à jour les statistiques si le jeu est terminé
    if (_gameModel.status == GameStatus.won) {
      _gamesPlayed++;
      _gamesWon++;
      if (_gameModel.attempts.length < _bestAttempts) {
        _bestAttempts = _gameModel.attempts.length;
      }
      _saveStats();
    } else if (_gameModel.status == GameStatus.lost) {
      _gamesPlayed++;
      _saveStats();
    }

    notifyListeners();
    return attempt;
  }

  void restartGame() {
    _gameModel.restart();
    notifyListeners();
  }

  void changeDifficulty(GameDifficulty difficulty) {
    _difficulty = difficulty;
    _gameModel = difficulty.getGameModel();
    notifyListeners();
  }

  // Méthodes pour les statistiques et paramètres
  Future<void> _loadStats() async {
    final prefs = await SharedPreferences.getInstance();
    _gamesPlayed = prefs.getInt('gamesPlayed') ?? 0;
    _gamesWon = prefs.getInt('gamesWon') ?? 0;
    _bestAttempts = prefs.getInt('bestAttempts') ?? 999;

    final difficultyIndex =
        prefs.getInt('difficulty') ?? 1; // Medium par défaut
    _difficulty = GameDifficulty.values[difficultyIndex];
    _gameModel = _difficulty.getGameModel();

    notifyListeners();
  }

  Future<void> _saveStats() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('gamesPlayed', _gamesPlayed);
    await prefs.setInt('gamesWon', _gamesWon);
    await prefs.setInt('bestAttempts', _bestAttempts);
    await prefs.setInt('difficulty', _difficulty.index);
  }

  void resetStats() {
    _gamesPlayed = 0;
    _gamesWon = 0;
    _bestAttempts = 999;
    _saveStats();
    notifyListeners();
  }

  // Méthodes pour le jeu personnalisé
  void setCustomGame({
    required int codeLength,
    required int maxAttempts,
    required bool allowDuplicates,
    int maxHints = 3,
  }) {
    _gameModel = GameModel(
      codeLength: codeLength,
      maxAttempts: maxAttempts,
      allowDuplicates: allowDuplicates,
      maxHints: maxHints,
    );
    _difficulty = GameDifficulty.custom;
    notifyListeners();
  }

  // Méthode pour obtenir un indice
  HintResult getHint() {
    final hint = _gameModel.getHint();
    notifyListeners();
    return hint;
  }
}
