import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../theme/app_theme.dart';

class NeonButton extends StatefulWidget {
  final String text;
  final VoidCallback onPressed;
  final LinearGradient gradient;
  final double width;
  final double height;
  final double borderRadius;
  final bool isAnimated;
  final IconData? icon;
  
  const NeonButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.gradient = AppTheme.primaryGradient,
    this.width = 200,
    this.height = 60,
    this.borderRadius = 30,
    this.isAnimated = true,
    this.icon,
  });
  
  @override
  State<NeonButton> createState() => _NeonButtonState();
}

class _NeonButtonState extends State<NeonButton> {
  bool _isPressed = false;
  
  @override
  Widget build(BuildContext context) {
    final buttonWidget = GestureDetector(
      onTapDown: (_) => setState(() => _isPressed = true),
      onTapUp: (_) {
        setState(() => _isPressed = false);
        widget.onPressed();
      },
      onTapCancel: () => setState(() => _isPressed = false),
      child: Container(
        width: widget.width,
        height: widget.height,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(widget.borderRadius),
          gradient: widget.gradient,
          boxShadow: _isPressed 
              ? [] 
              : AppTheme.getNeonGlow(widget.gradient.colors.first),
        ),
        child: Center(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (widget.icon != null) ...[
                Icon(
                  widget.icon,
                  color: Colors.white,
                  size: 24,
                ),
                const SizedBox(width: 10),
              ],
              Text(
                widget.text,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 1.5,
                ),
              ),
            ],
          ),
        ),
      ),
    );
    
    if (widget.isAnimated) {
      return buttonWidget
        .animate(onPlay: (controller) => controller.repeat())
        .shimmer(
          duration: const Duration(seconds: 2),
          color: Colors.white.withOpacity(0.2),
        )
        .animate(
          onPlay: (controller) => controller.repeat(reverse: true),
          delay: 500.ms,
        )
        .scale(
          begin: const Offset(1, 1),
          end: const Offset(1.02, 1.02),
          duration: const Duration(seconds: 1),
        );
    }
    
    return buttonWidget;
  }
}
