import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

enum AchievementType {
  firstWin,
  winStreak,
  perfectDeduction,
  luckyGuess,
  persistent,
  masterMind,
  strategist,
  collector,
  speedster,
  noHints,
}

class Achievement {
  final String id;
  final String title;
  final String description;
  final IconData icon;
  final Color color;
  final AchievementType type;
  final int requiredValue;
  final bool isSecret;
  
  const Achievement({
    required this.id,
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
    required this.type,
    required this.requiredValue,
    this.isSecret = false,
  });
  
  // Méthode pour vérifier si un succès est débloqué en fonction des statistiques
  bool isUnlocked({
    required int gamesPlayed,
    required int gamesWon,
    required int bestAttempts,
    required int currentStreak,
    required int maxStreak,
    required int totalHintsUsed,
    required int perfectGames,
    required int oneAttemptWins,
    required int persistentWins,
    required int fastGames,
  }) {
    switch (type) {
      case AchievementType.firstWin:
        return gamesWon >= requiredValue;
      case AchievementType.winStreak:
        return maxStreak >= requiredValue;
      case AchievementType.perfectDeduction:
        return perfectGames >= requiredValue;
      case AchievementType.luckyGuess:
        return oneAttemptWins >= requiredValue;
      case AchievementType.persistent:
        return persistentWins >= requiredValue;
      case AchievementType.masterMind:
        return gamesWon >= requiredValue;
      case AchievementType.strategist:
        return (gamesWon >= requiredValue && totalHintsUsed <= requiredValue);
      case AchievementType.collector:
        // Nombre de succès débloqués, sera calculé séparément
        return false;
      case AchievementType.speedster:
        return fastGames >= requiredValue;
      case AchievementType.noHints:
        return (gamesWon >= requiredValue && totalHintsUsed == 0);
    }
  }
}

// Liste des succès disponibles
class Achievements {
  static const List<Achievement> all = [
    Achievement(
      id: 'first_win',
      title: 'First Blood',
      description: 'Win your first game',
      icon: Icons.emoji_events,
      color: AppTheme.neonGreen,
      type: AchievementType.firstWin,
      requiredValue: 1,
    ),
    Achievement(
      id: 'win_streak_3',
      title: 'On Fire',
      description: 'Win 3 games in a row',
      icon: Icons.local_fire_department,
      color: AppTheme.neonPink,
      type: AchievementType.winStreak,
      requiredValue: 3,
    ),
    Achievement(
      id: 'win_streak_5',
      title: 'Unstoppable',
      description: 'Win 5 games in a row',
      icon: Icons.bolt,
      color: AppTheme.neonYellow,
      type: AchievementType.winStreak,
      requiredValue: 5,
    ),
    Achievement(
      id: 'perfect_deduction',
      title: 'Perfect Deduction',
      description: 'Win a game in 4 attempts or less',
      icon: Icons.psychology,
      color: AppTheme.neonBlue,
      type: AchievementType.perfectDeduction,
      requiredValue: 1,
    ),
    Achievement(
      id: 'lucky_guess',
      title: 'Lucky Guess',
      description: 'Win a game in just 1 attempt',
      icon: Icons.casino,
      color: AppTheme.neonPurple,
      type: AchievementType.luckyGuess,
      requiredValue: 1,
      isSecret: true,
    ),
    Achievement(
      id: 'persistent',
      title: 'Persistent',
      description: 'Win after using 8 or more attempts',
      icon: Icons.fitness_center,
      color: AppTheme.neonGreen,
      type: AchievementType.persistent,
      requiredValue: 1,
    ),
    Achievement(
      id: 'master_mind_10',
      title: 'Code Breaker',
      description: 'Win 10 games',
      icon: Icons.security,
      color: AppTheme.neonBlue,
      type: AchievementType.masterMind,
      requiredValue: 10,
    ),
    Achievement(
      id: 'master_mind_50',
      title: 'Master Mind',
      description: 'Win 50 games',
      icon: Icons.workspace_premium,
      color: AppTheme.neonYellow,
      type: AchievementType.masterMind,
      requiredValue: 50,
    ),
    Achievement(
      id: 'strategist',
      title: 'Strategist',
      description: 'Win 5 games using less than 3 hints in total',
      icon: Icons.lightbulb,
      color: AppTheme.neonPink,
      type: AchievementType.strategist,
      requiredValue: 5,
    ),
    Achievement(
      id: 'no_hints_5',
      title: 'Self-Reliant',
      description: 'Win 5 games without using any hints',
      icon: Icons.do_not_disturb,
      color: AppTheme.neonPurple,
      type: AchievementType.noHints,
      requiredValue: 5,
    ),
    Achievement(
      id: 'speedster',
      title: 'Speedster',
      description: 'Win a game in less than 30 seconds',
      icon: Icons.speed,
      color: AppTheme.neonGreen,
      type: AchievementType.speedster,
      requiredValue: 1,
      isSecret: true,
    ),
  ];
  
  // Méthode pour obtenir un succès par son ID
  static Achievement? getById(String id) {
    try {
      return all.firstWhere((achievement) => achievement.id == id);
    } catch (e) {
      return null;
    }
  }
}
