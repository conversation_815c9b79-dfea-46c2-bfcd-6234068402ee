import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/game_provider.dart';
import '../models/game_model.dart';
import '../services/audio_service.dart';
import '../theme/app_theme.dart';
import '../widgets/neon_button.dart';
import '../widgets/audio_controls.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  late GameDifficulty _selectedDifficulty;

  // Paramètres personnalisés
  int _customCodeLength = 4;
  int _customMaxAttempts = 10;
  bool _customAllowDuplicates = false;
  int _customMaxHints = 3;

  @override
  void initState() {
    super.initState();
    final gameProvider = Provider.of<GameProvider>(context, listen: false);
    _selectedDifficulty = gameProvider.difficulty;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('SETTINGS'),
        backgroundColor: Colors.transparent,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [AppTheme.neonPurple, AppTheme.neonBlue],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [AppTheme.darkBackground, Color(0xFF121212)],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSectionTitle(context, 'Game Difficulty'),
                const SizedBox(height: 16),

                _buildDifficultySelector(),
                const SizedBox(height: 32),

                if (_selectedDifficulty == GameDifficulty.custom)
                  _buildCustomSettings(context),

                _buildSectionTitle(context, 'Audio'),
                const SizedBox(height: 16),

                const AudioControls(),
                const SizedBox(height: 32),

                _buildSectionTitle(context, 'About the Game'),
                const SizedBox(height: 16),

                _buildAboutSection(context),
                const SizedBox(height: 32),

                Center(
                  child: NeonButton(
                    text: 'SAVE SETTINGS',
                    width: 250,
                    gradient: AppTheme.secondaryGradient,
                    onPressed: _saveSettings,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(BuildContext context, String title) {
    return ShaderMask(
      shaderCallback:
          (bounds) => const LinearGradient(
            colors: [AppTheme.neonPink, AppTheme.neonBlue],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ).createShader(bounds),
      child: Text(
        title.toUpperCase(),
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.bold,
          letterSpacing: 1.5,
        ),
      ),
    );
  }

  Widget _buildDifficultySelector() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: AppTheme.darkBackgroundSecondary,
        boxShadow: AppTheme.getNeonGlow(AppTheme.neonBlue, intensity: 0.3),
      ),
      child: Column(
        children: [
          _buildDifficultyOption(
            'Easy',
            'Code length: 3, Attempts: 12',
            GameDifficulty.easy,
            AppTheme.neonGreen,
          ),
          const Divider(color: Colors.white24),

          _buildDifficultyOption(
            'Medium',
            'Code length: 4, Attempts: 10',
            GameDifficulty.medium,
            AppTheme.neonBlue,
          ),
          const Divider(color: Colors.white24),

          _buildDifficultyOption(
            'Hard',
            'Code length: 5, Attempts: 8, Duplicates allowed',
            GameDifficulty.hard,
            AppTheme.neonPink,
          ),
          const Divider(color: Colors.white24),

          _buildDifficultyOption(
            'Custom',
            'Define your own rules',
            GameDifficulty.custom,
            AppTheme.neonPurple,
          ),
        ],
      ),
    );
  }

  Widget _buildDifficultyOption(
    String title,
    String description,
    GameDifficulty difficulty,
    Color color,
  ) {
    return RadioListTile<GameDifficulty>(
      title: Text(
        title,
        style: TextStyle(color: color, fontWeight: FontWeight.bold),
      ),
      subtitle: Text(
        description,
        style: const TextStyle(color: Colors.white70),
      ),
      value: difficulty,
      groupValue: _selectedDifficulty,
      activeColor: color,
      onChanged: (value) {
        setState(() {
          _selectedDifficulty = value!;
        });
      },
    );
  }

  Widget _buildCustomSettings(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 32),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: AppTheme.darkBackgroundSecondary,
        boxShadow: AppTheme.getNeonGlow(AppTheme.neonPurple, intensity: 0.3),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'CUSTOM SETTINGS',
            style: TextStyle(
              color: AppTheme.neonPurple,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Code length slider
          Text(
            'Code Length: $_customCodeLength',
            style: const TextStyle(color: Colors.white),
          ),
          Slider(
            value: _customCodeLength.toDouble(),
            min: 3,
            max: 6,
            divisions: 3,
            activeColor: AppTheme.neonPurple,
            inactiveColor: Colors.white24,
            onChanged: (value) {
              setState(() {
                _customCodeLength = value.toInt();
              });
            },
          ),

          const SizedBox(height: 16),

          // Max attempts slider
          Text(
            'Max Attempts: $_customMaxAttempts',
            style: const TextStyle(color: Colors.white),
          ),
          Slider(
            value: _customMaxAttempts.toDouble(),
            min: 5,
            max: 15,
            divisions: 10,
            activeColor: AppTheme.neonPurple,
            inactiveColor: Colors.white24,
            onChanged: (value) {
              setState(() {
                _customMaxAttempts = value.toInt();
              });
            },
          ),

          const SizedBox(height: 16),

          // Max hints slider
          Text(
            'Max Hints: $_customMaxHints',
            style: const TextStyle(color: Colors.white),
          ),
          Slider(
            value: _customMaxHints.toDouble(),
            min: 0,
            max: 5,
            divisions: 5,
            activeColor: AppTheme.neonPurple,
            inactiveColor: Colors.white24,
            onChanged: (value) {
              setState(() {
                _customMaxHints = value.toInt();
              });
            },
          ),

          const SizedBox(height: 16),

          // Allow duplicates switch
          SwitchListTile(
            title: const Text(
              'Allow Duplicate Digits',
              style: TextStyle(color: Colors.white),
            ),
            value: _customAllowDuplicates,
            activeColor: AppTheme.neonPurple,
            onChanged: (value) {
              setState(() {
                _customAllowDuplicates = value;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAboutSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: AppTheme.darkBackgroundSecondary,
        boxShadow: AppTheme.getNeonGlow(AppTheme.neonBlue, intensity: 0.3),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Bulls and Cows is a code-breaking game. After each guess, you get feedback:',
            style: TextStyle(color: Colors.white),
          ),
          const SizedBox(height: 16),

          _buildRuleItem(
            context,
            'Bulls',
            'Correct digit in the correct position',
            AppTheme.neonGreen,
          ),
          const SizedBox(height: 8),

          _buildRuleItem(
            context,
            'Cows',
            'Correct digit in the wrong position',
            AppTheme.neonYellow,
          ),
          const SizedBox(height: 16),

          const Text(
            'Try to guess the secret code in as few attempts as possible!',
            style: TextStyle(color: Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildRuleItem(
    BuildContext context,
    String title,
    String description,
    Color color,
  ) {
    return Row(
      children: [
        Container(
          width: 16,
          height: 16,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: color,
            boxShadow: AppTheme.getNeonGlow(color, intensity: 0.5),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: '$title: ',
                  style: TextStyle(color: color, fontWeight: FontWeight.bold),
                ),
                TextSpan(
                  text: description,
                  style: const TextStyle(color: Colors.white70),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  void _saveSettings() {
    final gameProvider = Provider.of<GameProvider>(context, listen: false);
    final audioService = Provider.of<AudioService>(context, listen: false);

    // Jouer le son de bouton
    audioService.playSound(SoundType.buttonClick);

    // Mettre à jour la difficulté
    if (_selectedDifficulty == GameDifficulty.custom) {
      gameProvider.setCustomGame(
        codeLength: _customCodeLength,
        maxAttempts: _customMaxAttempts,
        allowDuplicates: _customAllowDuplicates,
        maxHints: _customMaxHints,
      );
    } else if (gameProvider.difficulty != _selectedDifficulty) {
      gameProvider.changeDifficulty(_selectedDifficulty);
    }

    // Afficher un message de confirmation
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Settings saved!'),
        backgroundColor: AppTheme.neonGreen,
        behavior: SnackBarBehavior.floating,
      ),
    );

    // Retourner à l'écran précédent
    Navigator.pop(context);
  }
}
