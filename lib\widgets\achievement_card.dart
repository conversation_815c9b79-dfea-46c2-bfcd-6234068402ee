import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../models/achievement_model.dart';
import '../theme/app_theme.dart';

class AchievementCard extends StatelessWidget {
  final Achievement achievement;
  final bool isUnlocked;
  final bool isNew;
  
  const AchievementCard({
    Key? key,
    required this.achievement,
    required this.isUnlocked,
    this.isNew = false,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: LinearGradient(
          colors: isUnlocked 
              ? [achievement.color.withAlpha(40), AppTheme.darkBackgroundSecondary]
              : [Colors.grey.withAlpha(30), AppTheme.darkBackgroundSecondary],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: isUnlocked 
            ? AppTheme.getNeonGlow(achievement.color, intensity: isNew ? 0.7 : 0.3)
            : null,
        border: Border.all(
          color: isUnlocked ? achievement.color.withAlpha(100) : Colors.grey.withAlpha(50),
          width: 1,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Stack(
          children: [
            // Contenu principal
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // Icône
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: isUnlocked
                          ? LinearGradient(
                              colors: [achievement.color, achievement.color.withAlpha(150)],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            )
                          : LinearGradient(
                              colors: [Colors.grey.shade700, Colors.grey.shade800],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                      boxShadow: isUnlocked
                          ? AppTheme.getNeonGlow(achievement.color, intensity: 0.5)
                          : null,
                    ),
                    child: Icon(
                      isUnlocked || !achievement.isSecret
                          ? achievement.icon
                          : Icons.lock,
                      color: isUnlocked ? Colors.white : Colors.grey.shade500,
                      size: 30,
                    ),
                  ),
                  
                  const SizedBox(width: 16),
                  
                  // Titre et description
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          isUnlocked || !achievement.isSecret
                              ? achievement.title
                              : 'Secret Achievement',
                          style: TextStyle(
                            color: isUnlocked ? achievement.color : Colors.grey,
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          isUnlocked || !achievement.isSecret
                              ? achievement.description
                              : 'Keep playing to discover this achievement',
                          style: TextStyle(
                            color: isUnlocked ? Colors.white70 : Colors.grey.shade600,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            
            // Badge "NEW" si c'est un nouveau succès
            if (isNew)
              Positioned(
                top: 0,
                right: 0,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [AppTheme.neonPink, AppTheme.neonPurple],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(16),
                    ),
                    boxShadow: AppTheme.getNeonGlow(AppTheme.neonPink, intensity: 0.5),
                  ),
                  child: const Text(
                    'NEW',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    ).animate(target: isNew ? 1 : 0)
      .shimmer(
        duration: const Duration(seconds: 2),
        color: achievement.color.withAlpha(150),
      )
      .animate(target: isNew ? 1 : 0)
      .scale(
        begin: const Offset(1, 1),
        end: const Offset(1.03, 1.03),
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      )
      .then()
      .scale(
        begin: const Offset(1.03, 1.03),
        end: const Offset(1, 1),
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
  }
}
