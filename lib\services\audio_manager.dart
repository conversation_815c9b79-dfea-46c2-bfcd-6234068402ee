import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Une classe simplifiée pour gérer l'audio sans dépendre de fichiers réels
/// Cette classe simule le comportement audio et peut être remplacée par une
/// implémentation réelle une fois que les fichiers audio sont disponibles
class AudioManager extends ChangeNotifier {
  // Singleton
  static final AudioManager _instance = AudioManager._internal();
  
  factory AudioManager() {
    return _instance;
  }
  
  AudioManager._internal() {
    _loadSettings();
  }
  
  // États audio
  bool _musicEnabled = true;
  bool _soundEffectsEnabled = true;
  double _musicVolume = 1.0;
  double _effectsVolume = 1.0;
  
  // Getters
  bool get isMusicEnabled => _musicEnabled;
  bool get isSoundEffectsEnabled => _soundEffectsEnabled;
  double get musicVolume => _musicVolume;
  double get effectsVolume => _effectsVolume;
  
  // Chargement des paramètres
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _musicEnabled = prefs.getBool('music_enabled') ?? true;
      _soundEffectsEnabled = prefs.getBool('sound_effects_enabled') ?? true;
      _musicVolume = prefs.getDouble('music_volume') ?? 1.0;
      _effectsVolume = prefs.getDouble('effects_volume') ?? 1.0;
      notifyListeners();
    } catch (e) {
      if (kDebugMode) {
        print('Error loading audio settings: $e');
      }
    }
  }
  
  // Sauvegarde des paramètres
  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('music_enabled', _musicEnabled);
      await prefs.setBool('sound_effects_enabled', _soundEffectsEnabled);
      await prefs.setDouble('music_volume', _musicVolume);
      await prefs.setDouble('effects_volume', _effectsVolume);
    } catch (e) {
      if (kDebugMode) {
        print('Error saving audio settings: $e');
      }
    }
  }
  
  // Méthodes pour la musique
  void playHomeMusic() {
    if (_musicEnabled) {
      if (kDebugMode) {
        print('Playing home music at volume: $_musicVolume');
        print('Note: This is a placeholder. Replace with real audio implementation.');
      }
    }
  }
  
  void playGameMusic() {
    if (_musicEnabled) {
      if (kDebugMode) {
        print('Playing game music at volume: $_musicVolume');
        print('Note: This is a placeholder. Replace with real audio implementation.');
      }
    }
  }
  
  void stopMusic() {
    if (kDebugMode) {
      print('Stopping music');
    }
  }
  
  void pauseMusic() {
    if (kDebugMode) {
      print('Pausing music');
    }
  }
  
  void resumeMusic() {
    if (_musicEnabled) {
      if (kDebugMode) {
        print('Resuming music');
      }
    }
  }
  
  // Méthodes pour les effets sonores
  void playSound(String soundName) {
    if (_soundEffectsEnabled) {
      if (kDebugMode) {
        print('Playing sound effect: $soundName at volume: $_effectsVolume');
        print('Note: This is a placeholder. Replace with real audio implementation.');
      }
    }
  }
  
  // Méthodes pour activer/désactiver les sons
  void toggleMusic() {
    _musicEnabled = !_musicEnabled;
    if (kDebugMode) {
      print('Music ${_musicEnabled ? 'enabled' : 'disabled'}');
    }
    _saveSettings();
    notifyListeners();
  }
  
  void toggleSoundEffects() {
    _soundEffectsEnabled = !_soundEffectsEnabled;
    if (kDebugMode) {
      print('Sound effects ${_soundEffectsEnabled ? 'enabled' : 'disabled'}');
    }
    _saveSettings();
    notifyListeners();
  }
  
  // Méthodes pour régler le volume
  void setMusicVolume(double volume) {
    _musicVolume = volume.clamp(0.0, 1.0);
    if (kDebugMode) {
      print('Music volume set to: $_musicVolume');
    }
    _saveSettings();
    notifyListeners();
  }
  
  void setEffectsVolume(double volume) {
    _effectsVolume = volume.clamp(0.0, 1.0);
    if (kDebugMode) {
      print('Effects volume set to: $_effectsVolume');
    }
    _saveSettings();
    notifyListeners();
  }
}
