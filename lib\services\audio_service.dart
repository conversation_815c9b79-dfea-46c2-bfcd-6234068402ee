import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

enum SoundType {
  buttonClick,
  digitPress,
  submitGuess,
  correctGuess,
  gameWin,
  gameLose,
  achievement,
}

class AudioService extends ChangeNotifier {
  // Instances des lecteurs audio
  final AudioPlayer _musicPlayer = AudioPlayer();
  final AudioPlayer _effectsPlayer = AudioPlayer();

  // États audio
  bool _musicEnabled = true;
  bool _soundEffectsEnabled = true;
  double _musicVolume = 1.0; // Volume maximal par défaut
  double _effectsVolume = 1.0; // Volume maximal par défaut

  // Chemins des fichiers audio
  static const String _homeMusicPath = 'lib/assets/sounds/home_music.mp3';
  static const String _gameMusicPath = 'lib/assets/sounds/game_music.mp3';

  static const Map<SoundType, String> _soundEffects = {
    SoundType.buttonClick: 'lib/assets/sounds/button_click.mp3',
    SoundType.digitPress: 'lib/assets/sounds/digit_press.mp3',
    SoundType.submitGuess: 'lib/assets/sounds/submit_guess.mp3',
    SoundType.correctGuess: 'lib/assets/sounds/correct_guess.mp3',
    SoundType.gameWin: 'lib/assets/sounds/game_win.mp3',
    SoundType.gameLose: 'lib/assets/sounds/game_lose.mp3',
    SoundType.achievement: 'lib/assets/sounds/achievement.mp3',
  };

  // Getters
  bool get isMusicEnabled => _musicEnabled;
  bool get isSoundEffectsEnabled => _soundEffectsEnabled;
  double get musicVolume => _musicVolume;
  double get effectsVolume => _effectsVolume;

  // Constructeur
  AudioService() {
    _init();
  }

  // Initialisation
  Future<void> _init() async {
    await _loadSettings();

    // Configuration des lecteurs audio
    await _musicPlayer.setReleaseMode(ReleaseMode.loop);
    await _musicPlayer.setVolume(_musicEnabled ? _musicVolume : 0);

    await _effectsPlayer.setReleaseMode(ReleaseMode.release);
    await _effectsPlayer.setVolume(_soundEffectsEnabled ? _effectsVolume : 0);
  }

  // Chargement des paramètres audio depuis les préférences
  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();

    _musicEnabled = prefs.getBool('music_enabled') ?? true;
    _soundEffectsEnabled = prefs.getBool('sound_effects_enabled') ?? true;
    _musicVolume =
        prefs.getDouble('music_volume') ?? 1.0; // Volume maximal par défaut
    _effectsVolume =
        prefs.getDouble('effects_volume') ?? 1.0; // Volume maximal par défaut

    // Appliquer immédiatement les paramètres de volume
    await _musicPlayer.setVolume(_musicEnabled ? _musicVolume : 0);
    await _effectsPlayer.setVolume(_soundEffectsEnabled ? _effectsVolume : 0);

    notifyListeners();
  }

  // Sauvegarde des paramètres audio
  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();

    await prefs.setBool('music_enabled', _musicEnabled);
    await prefs.setBool('sound_effects_enabled', _soundEffectsEnabled);
    await prefs.setDouble('music_volume', _musicVolume);
    await prefs.setDouble('effects_volume', _effectsVolume);
  }

  // Méthodes pour la musique
  Future<void> playHomeMusic() async {
    // Même si la musique est désactivée, on prépare le lecteur
    // pour pouvoir l'activer plus tard sans avoir à recharger le fichier

    try {
      // Arrêter la musique actuelle
      await _musicPlayer.stop();

      // S'assurer que le volume est correct
      await _musicPlayer.setVolume(_musicEnabled ? _musicVolume : 0);

      // Vérifier si le fichier existe et est valide
      try {
        // Charger et jouer la musique
        await _musicPlayer.setSource(AssetSource(_homeMusicPath));

        // Jouer la musique si elle est activée
        if (_musicEnabled) {
          await _musicPlayer.resume();
          if (kDebugMode) {
            print('Home music started playing at volume: $_musicVolume');
          }
        }
      } catch (audioError) {
        // Si le fichier n'est pas valide, essayer avec game_music comme fallback
        if (kDebugMode) {
          print('Home music file not valid or not found: $audioError');
          print('Trying to use game music as fallback...');
        }

        try {
          await _musicPlayer.setSource(AssetSource(_gameMusicPath));
          if (_musicEnabled) {
            await _musicPlayer.resume();
          }
        } catch (fallbackError) {
          if (kDebugMode) {
            print('Fallback music also failed: $fallbackError');
            print('Please add valid MP3 files to the assets/sounds folder.');
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error playing home music: $e');
      }
    }
  }

  Future<void> playGameMusic() async {
    // Même si la musique est désactivée, on prépare le lecteur
    // pour pouvoir l'activer plus tard sans avoir à recharger le fichier

    try {
      // Arrêter la musique actuelle
      await _musicPlayer.stop();

      // S'assurer que le volume est correct
      await _musicPlayer.setVolume(_musicEnabled ? _musicVolume : 0);

      // Vérifier si le fichier existe et est valide
      try {
        // Charger et jouer la musique
        await _musicPlayer.setSource(AssetSource(_gameMusicPath));

        // Jouer la musique si elle est activée
        if (_musicEnabled) {
          await _musicPlayer.resume();
          if (kDebugMode) {
            print('Game music started playing at volume: $_musicVolume');
          }
        }
      } catch (audioError) {
        // Si le fichier n'est pas valide, essayer avec home_music comme fallback
        if (kDebugMode) {
          print('Game music file not valid or not found: $audioError');
          print('Trying to use home music as fallback...');
        }

        try {
          await _musicPlayer.setSource(AssetSource(_homeMusicPath));
          if (_musicEnabled) {
            await _musicPlayer.resume();
          }
        } catch (fallbackError) {
          if (kDebugMode) {
            print('Fallback music also failed: $fallbackError');
            print('Please add valid MP3 files to the assets/sounds folder.');
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error playing game music: $e');
      }
    }
  }

  Future<void> stopMusic() async {
    await _musicPlayer.stop();
  }

  Future<void> pauseMusic() async {
    await _musicPlayer.pause();
  }

  Future<void> resumeMusic() async {
    if (_musicEnabled) {
      await _musicPlayer.resume();
    }
  }

  // Méthodes pour les effets sonores
  Future<void> playSound(SoundType soundType) async {
    if (!_soundEffectsEnabled) return;

    final soundPath = _soundEffects[soundType];
    if (soundPath == null) return;

    try {
      await _effectsPlayer.stop();

      // Vérifier si le fichier existe et est valide
      try {
        await _effectsPlayer.setSource(AssetSource(soundPath));
        await _effectsPlayer.resume();
      } catch (audioError) {
        // Si le fichier n'est pas valide, on affiche juste un message
        if (kDebugMode) {
          print('Sound effect file not valid or not found: $soundPath');
          print('Please replace the placeholder with a real MP3 file.');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error playing sound effect: $e');
      }
    }
  }

  // Méthodes pour activer/désactiver les sons
  void toggleMusic() {
    _musicEnabled = !_musicEnabled;

    if (_musicEnabled) {
      _musicPlayer.setVolume(_musicVolume);
      resumeMusic();
    } else {
      _musicPlayer.setVolume(0);
      // Ne pas arrêter la musique, juste couper le son
    }

    _saveSettings();
    notifyListeners();
  }

  void toggleSoundEffects() {
    _soundEffectsEnabled = !_soundEffectsEnabled;
    _effectsPlayer.setVolume(_soundEffectsEnabled ? _effectsVolume : 0);

    _saveSettings();
    notifyListeners();
  }

  // Méthodes pour régler le volume
  void setMusicVolume(double volume) {
    _musicVolume = volume.clamp(0.0, 1.0);
    if (_musicEnabled) {
      _musicPlayer.setVolume(_musicVolume);
    }

    _saveSettings();
    notifyListeners();
  }

  void setEffectsVolume(double volume) {
    _effectsVolume = volume.clamp(0.0, 1.0);
    if (_soundEffectsEnabled) {
      _effectsPlayer.setVolume(_effectsVolume);
    }

    _saveSettings();
    notifyListeners();
  }

  // Nettoyage des ressources
  @override
  void dispose() {
    _musicPlayer.dispose();
    _effectsPlayer.dispose();
    super.dispose();
  }
}
