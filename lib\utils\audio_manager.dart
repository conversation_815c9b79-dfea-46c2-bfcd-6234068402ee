import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/foundation.dart';

class AudioManager {
  static final AudioManager _instance = AudioManager._internal();
  final AudioPlayer _audioPlayer = AudioPlayer();
  bool _soundEnabled = true;

  factory AudioManager() {
    return _instance;
  }

  AudioManager._internal();

  void setSoundEnabled(bool enabled) {
    _soundEnabled = enabled;
  }

  Future<void> playButtonClick() async {
    if (!_soundEnabled) return;
    await _playSound('button_click.mp3');
  }

  Future<void> playSuccess() async {
    if (!_soundEnabled) return;
    await _playSound('success.mp3');
  }

  Future<void> playError() async {
    if (!_soundEnabled) return;
    await _playSound('error.mp3');
  }

  Future<void> playGameOver() async {
    if (!_soundEnabled) return;
    await _playSound('game_over.mp3');
  }

  Future<void> _playSound(String soundFile) async {
    try {
      // Commenté pour éviter les erreurs lors du lancement initial
      // await _audioPlayer.play(AssetSource('sounds/$soundFile'));
      if (kDebugMode) {
        print('Playing sound: $soundFile');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error playing sound: $e');
      }
    }
  }

  void dispose() {
    _audioPlayer.dispose();
  }
}
