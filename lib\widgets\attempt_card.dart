import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../models/game_model.dart';
import '../theme/app_theme.dart';

class AttemptCard extends StatelessWidget {
  final Attempt attempt;
  final bool isLatest;

  const AttemptCard({super.key, required this.attempt, this.isLatest = false});

  @override
  Widget build(BuildContext context) {
    // Calculer les dimensions en fonction de la taille de l'écran
    final screenWidth = MediaQuery.of(context).size.width;
    final circleSize = screenWidth < 360 ? 32.0 : 40.0;
    final fontSize = screenWidth < 360 ? 16.0 : 18.0;

    final card = Container(
      margin: EdgeInsets.symmetric(
        vertical: 8,
        horizontal: screenWidth < 360 ? 8.0 : 16.0,
      ),
      padding: EdgeInsets.all(screenWidth < 360 ? 12.0 : 16.0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: const LinearGradient(
          colors: [AppTheme.darkBackgroundSecondary, Color(0xFF1A1A1A)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow:
            isLatest
                ? AppTheme.getNeonGlow(AppTheme.neonBlue, intensity: 0.5)
                : null,
      ),
      child: Row(
        children: [
          // Numéro de tentative
          Container(
            width: circleSize,
            height: circleSize,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: AppTheme.primaryGradient,
            ),
            child: Center(
              child: Text(
                '${attempt.attemptNumber}',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: fontSize,
                ),
              ),
            ),
          ),
          SizedBox(width: screenWidth < 360 ? 8.0 : 16.0),

          // Guess
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: _buildGuessDigits(context),
            ),
          ),

          SizedBox(width: screenWidth < 360 ? 8.0 : 16.0),

          // Bulls and Cows
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildResultRow(
                context,
                'Bulls',
                attempt.bulls,
                AppTheme.neonGreen,
              ),
              SizedBox(height: screenWidth < 360 ? 4.0 : 8.0),
              _buildResultRow(
                context,
                'Cows',
                attempt.cows,
                AppTheme.neonYellow,
              ),
            ],
          ),
        ],
      ),
    );

    if (isLatest) {
      return card
          .animate()
          .fadeIn(duration: 400.ms)
          .slideY(
            begin: 0.5,
            end: 0,
            duration: 400.ms,
            curve: Curves.easeOutQuad,
          );
    }

    return card;
  }

  List<Widget> _buildGuessDigits(BuildContext context) {
    // Calculer les dimensions en fonction de la taille de l'écran
    final screenWidth = MediaQuery.of(context).size.width;
    final digitWidth =
        screenWidth < 360 ? 30.0 : (screenWidth < 400 ? 35.0 : 40.0);
    final digitHeight =
        screenWidth < 360 ? 40.0 : (screenWidth < 400 ? 45.0 : 50.0);
    final digitMargin = screenWidth < 360 ? 2.0 : 4.0;
    final fontSize =
        screenWidth < 360 ? 18.0 : (screenWidth < 400 ? 20.0 : 24.0);

    return attempt.guess.split('').map((digit) {
      return Container(
        width: digitWidth,
        height: digitHeight,
        margin: EdgeInsets.symmetric(horizontal: digitMargin),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          gradient: AppTheme.secondaryGradient,
          boxShadow: AppTheme.getNeonGlow(AppTheme.neonBlue, intensity: 0.3),
        ),
        child: Center(
          child: Text(
            digit,
            style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: fontSize,
            ),
          ),
        ),
      );
    }).toList();
  }

  Widget _buildResultRow(
    BuildContext context,
    String label,
    int count,
    Color color,
  ) {
    // Calculer les dimensions en fonction de la taille de l'écran
    final screenWidth = MediaQuery.of(context).size.width;
    final dotSize = screenWidth < 360 ? 8.0 : (screenWidth < 400 ? 10.0 : 12.0);
    final dotMargin = screenWidth < 360 ? 1.0 : 2.0;
    final textStyle =
        screenWidth < 360
            ? Theme.of(context).textTheme.bodySmall
            : Theme.of(context).textTheme.bodyMedium;

    return Row(
      children: [
        Text('$label: ', style: textStyle),
        ...List.generate(
          count,
          (index) => Container(
            width: dotSize,
            height: dotSize,
            margin: EdgeInsets.symmetric(horizontal: dotMargin),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: color,
              boxShadow: AppTheme.getNeonGlow(color, intensity: 0.5),
            ),
          ),
        ),
      ],
    );
  }
}
