import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTheme {
  // Couleurs néon principales
  static const Color neonPink = Color(0xFFFF00FF);
  static const Color neonBlue = Color(0xFF00FFFF);
  static const Color neonGreen = Color(0xFF00FF66);
  static const Color neonYellow = Color(0xFFFFFF00);
  static const Color neonPurple = Color(0xFF9D00FF);
  
  // Couleurs de fond
  static const Color darkBackground = Color(0xFF0A0A0A);
  static const Color darkBackgroundSecondary = Color(0xFF121212);
  
  // Couleurs de texte
  static const Color textPrimary = Colors.white;
  static const Color textSecondary = Color(0xFFCCCCCC);
  
  // Dégradés
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [neonPink, neonBlue],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient secondaryGradient = LinearGradient(
    colors: [neonGreen, neonBlue],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient accentGradient = LinearGradient(
    colors: [neonPurple, neonPink],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // Effets de lueur (glow)
  static List<BoxShadow> getNeonGlow(Color color, {double intensity = 1.0}) {
    return [
      BoxShadow(
        color: color.withOpacity(0.3 * intensity),
        blurRadius: 15.0 * intensity,
        spreadRadius: 5.0 * intensity,
      ),
      BoxShadow(
        color: color.withOpacity(0.2 * intensity),
        blurRadius: 30.0 * intensity,
        spreadRadius: 10.0 * intensity,
      ),
    ];
  }
  
  // Thème de l'application
  static ThemeData getTheme() {
    return ThemeData(
      scaffoldBackgroundColor: darkBackground,
      brightness: Brightness.dark,
      primaryColor: neonBlue,
      colorScheme: const ColorScheme.dark(
        primary: neonBlue,
        secondary: neonPink,
        tertiary: neonGreen,
        surface: darkBackgroundSecondary,
      ),
      textTheme: GoogleFonts.orbitronTextTheme(
        const TextTheme(
          displayLarge: TextStyle(color: textPrimary, fontWeight: FontWeight.bold),
          displayMedium: TextStyle(color: textPrimary, fontWeight: FontWeight.bold),
          displaySmall: TextStyle(color: textPrimary, fontWeight: FontWeight.bold),
          headlineLarge: TextStyle(color: textPrimary, fontWeight: FontWeight.bold),
          headlineMedium: TextStyle(color: textPrimary, fontWeight: FontWeight.bold),
          headlineSmall: TextStyle(color: textPrimary, fontWeight: FontWeight.bold),
          titleLarge: TextStyle(color: textPrimary, fontWeight: FontWeight.bold),
          titleMedium: TextStyle(color: textPrimary),
          titleSmall: TextStyle(color: textPrimary),
          bodyLarge: TextStyle(color: textPrimary),
          bodyMedium: TextStyle(color: textPrimary),
          bodySmall: TextStyle(color: textSecondary),
          labelLarge: TextStyle(color: textPrimary, fontWeight: FontWeight.bold),
          labelMedium: TextStyle(color: textPrimary),
          labelSmall: TextStyle(color: textSecondary),
        ),
      ),
      useMaterial3: true,
    );
  }
}
