import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import '../models/game_provider.dart';
import '../models/game_model.dart';
import '../models/achievement_provider.dart';
import '../services/simple_audio.dart';
import '../theme/app_theme.dart';
import '../widgets/attempt_card.dart';
import '../widgets/code_display.dart';
import '../widgets/hint_dialog.dart';
import '../widgets/neon_button.dart';
import '../widgets/neon_keypad.dart';

class GameScreen extends StatefulWidget {
  const GameScreen({super.key});

  @override
  State<GameScreen> createState() => _GameScreenState();
}

class _GameScreenState extends State<GameScreen> {
  String _currentGuess = '';
  bool _showError = false;
  Set<String> _usedDigits = {};

  @override
  void initState() {
    super.initState();

    // Jouer la musique de jeu immédiatement
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;
      // Utiliser SimpleAudio directement sans Provider
      SimpleAudio().playGameMusic();

      if (kDebugMode) {
        print('Attempting to play game music immediately after frame render');
      }
    });
  }

  @override
  void dispose() {
    // Revenir à la musique d'accueil quand on quitte l'écran de jeu
    if (mounted) {
      // Utiliser SimpleAudio directement sans Provider
      SimpleAudio().playHomeMusic();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final gameProvider = Provider.of<GameProvider>(context);
    final codeLength = gameProvider.gameModel.codeLength;
    final attempts = gameProvider.attempts;
    final status = gameProvider.status;

    return Scaffold(
      appBar: AppBar(
        title: const Text('BULLS & COWS'),
        backgroundColor: Colors.transparent,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [AppTheme.neonPurple, AppTheme.neonBlue],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
        actions: [
          // Bouton d'indice
          Consumer<GameProvider>(
            builder: (context, gameProvider, _) {
              return IconButton(
                icon: const Icon(Icons.lightbulb_outline),
                onPressed: gameProvider.hasHintsAvailable ? _showHint : null,
                tooltip: 'Get Hint',
                color:
                    gameProvider.hasHintsAvailable
                        ? AppTheme.neonYellow
                        : Colors.grey,
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _restartGame,
            tooltip: 'Restart Game',
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [AppTheme.darkBackground, Color(0xFF121212)],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Affichage du code actuel
              CodeDisplay(
                currentCode: _currentGuess,
                codeLength: codeLength,
                showError: _showError,
              ),

              // Affichage des tentatives
              Expanded(
                child:
                    status == GameStatus.playing && attempts.isEmpty
                        ? _buildInstructions(context, codeLength)
                        : status != GameStatus.playing
                        ? _buildGameOver(
                          context,
                          status,
                          gameProvider.secretCode,
                        )
                        : _buildAttemptsList(attempts),
              ),

              // Clavier numérique
              if (status == GameStatus.playing)
                NeonKeypad(
                  onDigitPressed: (digit) => _onDigitPressed(digit, codeLength),
                  onDeletePressed: _onDeletePressed,
                  onSubmitPressed: () => _onSubmitPressed(gameProvider),
                  isSubmitEnabled: _currentGuess.length == codeLength,
                  usedDigits: _usedDigits,
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInstructions(BuildContext context, int codeLength) {
    return SingleChildScrollView(
      child: Center(
        child: Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: const LinearGradient(
              colors: [Color(0xFF1A1A1A), Color(0xFF0D0D0D)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            boxShadow: AppTheme.getNeonGlow(AppTheme.neonBlue, intensity: 0.3),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Instruction principale
              RichText(
                    textAlign: TextAlign.center,
                    text: TextSpan(
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        letterSpacing: 1.0,
                      ),
                      children: [
                        TextSpan(
                          text: 'Guess the ',
                          style: TextStyle(color: Colors.white),
                        ),
                        TextSpan(
                          text: '$codeLength-digit',
                          style: TextStyle(
                            color: AppTheme.neonBlue,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        TextSpan(
                          text: ' code',
                          style: TextStyle(color: Colors.white),
                        ),
                      ],
                    ),
                  )
                  .animate()
                  .fadeIn(duration: 600.ms)
                  .slideY(begin: 0.2, end: 0, duration: 600.ms),

              const SizedBox(height: 16),

              // Indicateurs Bulls et Cows
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  // Bulls
                  _buildIndicator(
                    context,
                    AppTheme.neonGreen,
                    'BULLS',
                    'Correct position',
                    0.ms,
                  ),

                  // Cows
                  _buildIndicator(
                    context,
                    AppTheme.neonYellow,
                    'COWS',
                    'Wrong position',
                    200.ms,
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Message d'action
              Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: 8,
                      horizontal: 16,
                    ),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      gradient: AppTheme.accentGradient,
                      boxShadow: AppTheme.getNeonGlow(
                        AppTheme.neonPink,
                        intensity: 0.4,
                      ),
                    ),
                    child: Text(
                      'Use the keypad to enter your guess',
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  )
                  .animate(delay: 400.ms)
                  .fadeIn(duration: 600.ms)
                  .slideY(begin: 0.2, end: 0, duration: 600.ms)
                  .then()
                  .animate(
                    onPlay: (controller) => controller.repeat(reverse: true),
                  )
                  .scale(
                    begin: const Offset(1, 1),
                    end: const Offset(1.03, 1.03),
                    duration: const Duration(milliseconds: 1500),
                  ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildIndicator(
    BuildContext context,
    Color color,
    String title,
    String description,
    Duration delay,
  ) {
    // Calculer la largeur disponible et adapter la taille des indicateurs
    final screenWidth = MediaQuery.of(context).size.width;
    final indicatorWidth = (screenWidth - 80) / 2; // 80 pour les marges

    return Container(
          width: indicatorWidth,
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            color: Colors.black.withAlpha(100),
            border: Border.all(color: color.withAlpha(100), width: 1),
            boxShadow: AppTheme.getNeonGlow(color, intensity: 0.2),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    colors: [color, color.withAlpha(180)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  boxShadow: AppTheme.getNeonGlow(color, intensity: 0.6),
                ),
              ),
              const SizedBox(height: 6),
              Text(
                title,
                style: TextStyle(
                  color: color,
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                  letterSpacing: 1.0,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                description,
                textAlign: TextAlign.center,
                style: TextStyle(color: Colors.white70, fontSize: 10),
              ),
            ],
          ),
        )
        .animate(delay: delay)
        .fadeIn(duration: 600.ms)
        .slideX(begin: 0.2, end: 0, duration: 600.ms);
  }

  Widget _buildAttemptsList(List<Attempt> attempts) {
    return ListView.builder(
      padding: const EdgeInsets.only(bottom: 16),
      itemCount: attempts.length,
      itemBuilder: (context, index) {
        // Afficher les tentatives de la plus récente à la plus ancienne
        final attempt = attempts[attempts.length - 1 - index];
        return AttemptCard(attempt: attempt, isLatest: index == 0);
      },
    );
  }

  Widget _buildGameOver(
    BuildContext context,
    GameStatus status,
    String secretCode,
  ) {
    final isWin = status == GameStatus.won;
    final gameProvider = Provider.of<GameProvider>(context);
    final attempts = gameProvider.attempts.length;

    return Center(
      child: Container(
            margin: const EdgeInsets.all(24),
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(24),
              gradient: LinearGradient(
                colors:
                    isWin
                        ? [
                          AppTheme.neonGreen.withAlpha(51),
                          AppTheme.darkBackgroundSecondary,
                        ]
                        : [
                          AppTheme.neonPink.withAlpha(51),
                          AppTheme.darkBackgroundSecondary,
                        ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              boxShadow:
                  isWin
                      ? AppTheme.getNeonGlow(AppTheme.neonGreen, intensity: 0.5)
                      : AppTheme.getNeonGlow(AppTheme.neonPink, intensity: 0.5),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                      isWin ? Icons.emoji_events : Icons.sentiment_dissatisfied,
                      color: isWin ? AppTheme.neonGreen : AppTheme.neonPink,
                      size: 64,
                    )
                    .animate(
                      onPlay: (controller) => controller.repeat(reverse: true),
                    )
                    .scale(
                      begin: const Offset(1, 1),
                      end: const Offset(1.1, 1.1),
                      duration: const Duration(seconds: 1),
                    ),

                const SizedBox(height: 24),

                Text(
                  isWin ? 'YOU WIN!' : 'GAME OVER',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    color: isWin ? AppTheme.neonGreen : AppTheme.neonPink,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 2,
                  ),
                ),

                const SizedBox(height: 16),

                Text(
                  isWin
                      ? 'Congratulations! You found the code in $attempts attempts.'
                      : 'Better luck next time!',
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.titleMedium,
                ),

                const SizedBox(height: 24),

                Text(
                  'The secret code was:',
                  style: Theme.of(context).textTheme.bodyLarge,
                ),

                const SizedBox(height: 16),

                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children:
                      secretCode.split('').map((digit) {
                        return Container(
                          width: 50,
                          height: 60,
                          margin: const EdgeInsets.symmetric(horizontal: 4),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            gradient:
                                isWin
                                    ? AppTheme.secondaryGradient
                                    : AppTheme.accentGradient,
                            boxShadow:
                                isWin
                                    ? AppTheme.getNeonGlow(
                                      AppTheme.neonGreen,
                                      intensity: 0.5,
                                    )
                                    : AppTheme.getNeonGlow(
                                      AppTheme.neonPink,
                                      intensity: 0.5,
                                    ),
                          ),
                          child: Center(
                            child: Text(
                              digit,
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 24,
                              ),
                            ),
                          ),
                        );
                      }).toList(),
                ),

                const SizedBox(height: 32),

                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    NeonButton(
                      text: 'PLAY AGAIN',
                      width: 160,
                      height: 50,
                      gradient:
                          isWin
                              ? AppTheme.secondaryGradient
                              : AppTheme.primaryGradient,
                      onPressed: _restartGame,
                    ),
                    const SizedBox(width: 16),
                    NeonButton(
                      text: 'HOME',
                      width: 120,
                      height: 50,
                      gradient: AppTheme.accentGradient,
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
              ],
            ),
          )
          .animate()
          .fadeIn(duration: 800.ms)
          .scale(
            begin: const Offset(0.8, 0.8),
            end: const Offset(1, 1),
            duration: 800.ms,
            curve: Curves.elasticOut,
          ),
    );
  }

  void _onDigitPressed(String digit, int codeLength) {
    if (_currentGuess.length < codeLength) {
      // Jouer le son de pression de chiffre
      SimpleAudio().playSound('digit_press');

      setState(() {
        _currentGuess += digit;
        _showError = false;
      });
    }
  }

  void _onDeletePressed() {
    if (_currentGuess.isNotEmpty) {
      // Jouer le son de bouton
      SimpleAudio().playSound('button_click');

      setState(() {
        _currentGuess = _currentGuess.substring(0, _currentGuess.length - 1);
        _showError = false;
      });
    }
  }

  void _onSubmitPressed(GameProvider gameProvider) {
    final codeLength = gameProvider.gameModel.codeLength;
    final achievementProvider = Provider.of<AchievementProvider>(
      context,
      listen: false,
    );

    if (_currentGuess.length == codeLength) {
      try {
        // Jouer le son de soumission
        SimpleAudio().playSound('submit_guess');

        // Enregistrer le temps de début si c'est la première tentative
        if (gameProvider.attempts.isEmpty) {
          achievementProvider.startGame();
        }

        gameProvider.makeGuess(_currentGuess);

        // Mettre à jour les statistiques pour les succès
        if (gameProvider.status != GameStatus.playing) {
          achievementProvider.updateStats(
            status: gameProvider.status,
            attempts: gameProvider.attempts.length,
            hintsUsed: gameProvider.gameModel.hintsUsed,
            isNewGame: true,
          );

          // Jouer le son de victoire ou de défaite
          if (gameProvider.status == GameStatus.won) {
            SimpleAudio().playSound('game_win');
          } else if (gameProvider.status == GameStatus.lost) {
            SimpleAudio().playSound('game_lose');
          }
        } else if (gameProvider.attempts.isNotEmpty) {
          // Vérifier si la dernière tentative a des bulls
          final lastAttempt = gameProvider.attempts.last;
          if (lastAttempt.bulls > 0) {
            SimpleAudio().playSound('correct_guess');
          }
        }

        setState(() {
          _currentGuess = '';
          _showError = false;

          // Si le jeu est configuré pour ne pas permettre les doublons,
          // on peut désactiver les chiffres déjà utilisés
          if (!gameProvider.gameModel.allowDuplicates) {
            _updateUsedDigits(gameProvider);
          }
        });
      } catch (e) {
        setState(() {
          _showError = true;
        });
      }
    } else {
      // Jouer le son d'erreur
      SimpleAudio().playSound('button_click');

      setState(() {
        _showError = true;
      });
    }
  }

  void _updateUsedDigits(GameProvider gameProvider) {
    // Cette méthode est une simplification - dans un jeu réel,
    // il faudrait une logique plus complexe pour déterminer quels chiffres
    // ne peuvent plus être utilisés

    // Pour l'instant, on ne désactive aucun chiffre
    _usedDigits = {};
  }

  void _restartGame() {
    final gameProvider = Provider.of<GameProvider>(context, listen: false);
    final achievementProvider = Provider.of<AchievementProvider>(
      context,
      listen: false,
    );

    // Jouer le son de bouton
    SimpleAudio().playSound('button_click');

    gameProvider.restartGame();
    achievementProvider.startGame(); // Réinitialiser le temps de début

    setState(() {
      _currentGuess = '';
      _showError = false;
      _usedDigits = {};
    });
  }

  void _showHint() {
    final gameProvider = Provider.of<GameProvider>(context, listen: false);
    final achievementProvider = Provider.of<AchievementProvider>(
      context,
      listen: false,
    );
    final hint = gameProvider.getHint();

    // Jouer le son de bouton
    SimpleAudio().playSound('button_click');

    // Mettre à jour les statistiques pour les succès
    if (gameProvider.status == GameStatus.playing) {
      achievementProvider.updateStats(
        status: gameProvider.status,
        attempts: gameProvider.attempts.length,
        hintsUsed: gameProvider.hintsUsed,
        isNewGame: false,
      );
    }

    showDialog(
      context: context,
      builder:
          (context) => HintDialog(
            hint: hint,
            hintsUsed: gameProvider.hintsUsed,
            maxHints: gameProvider.maxHints,
          ),
    );
  }
}
