import 'dart:math';

class GameModel {
  final int codeLength;
  final int maxAttempts;
  final bool allowDuplicates;
  final int maxHints;

  late String secretCode;
  List<Attempt> attempts = [];
  GameStatus status = GameStatus.playing;
  int hintsUsed = 0;

  GameModel({
    this.codeLength = 4,
    this.maxAttempts = 10,
    this.allowDuplicates = false,
    this.maxHints = 3,
  }) {
    generateSecretCode();
  }

  void generateSecretCode() {
    final random = Random();
    final digits = List<int>.generate(10, (i) => i);

    if (allowDuplicates) {
      secretCode = List.generate(codeLength, (_) => random.nextInt(10)).join();
    } else {
      // Shuffle the digits and take the first 'codeLength' digits
      digits.shuffle(random);
      secretCode = digits.take(codeLength).join();
    }
  }

  Attempt makeGuess(String guess) {
    if (status != GameStatus.playing) {
      throw Exception('Game is not in playing state');
    }

    if (guess.length != codeLength) {
      throw Exception('Guess must be $codeLength digits long');
    }

    // Calculate bulls and cows
    int bulls = 0;

    // First count bulls
    for (int i = 0; i < codeLength; i++) {
      if (guess[i] == secretCode[i]) {
        bulls++;
      }
    }

    // Then calculate cows using the more accurate method
    int cows = calculateCows(guess);

    // Create and add the attempt
    final attempt = Attempt(
      guess: guess,
      bulls: bulls,
      cows: cows,
      attemptNumber: attempts.length + 1,
    );

    attempts.add(attempt);

    // Update game status
    if (bulls == codeLength) {
      status = GameStatus.won;
    } else if (attempts.length >= maxAttempts) {
      status = GameStatus.lost;
    }

    return attempt;
  }

  void restart() {
    attempts = [];
    status = GameStatus.playing;
    hintsUsed = 0;
    generateSecretCode();
  }

  // Méthode pour obtenir un indice
  HintResult getHint() {
    if (hintsUsed >= maxHints || status != GameStatus.playing) {
      return HintResult(
        available: false,
        digit: '',
        isBull: false,
        position: -1,
      );
    }

    final random = Random();
    final position = random.nextInt(codeLength);
    final digit = secretCode[position];

    hintsUsed++;

    return HintResult(
      available: true,
      digit: digit,
      isBull: true,
      position: position,
    );
  }

  // Méthode pour vérifier si des indices sont disponibles
  bool hasHintsAvailable() {
    return hintsUsed < maxHints && status == GameStatus.playing;
  }

  // More accurate cows calculation for handling duplicates
  int calculateCows(String guess) {
    Map<String, int> secretDigitCount = {};
    Map<String, int> guessDigitCount = {};

    // Count occurrences of each digit in secret code and guess
    for (int i = 0; i < codeLength; i++) {
      if (guess[i] != secretCode[i]) {
        secretDigitCount[secretCode[i]] =
            (secretDigitCount[secretCode[i]] ?? 0) + 1;
        guessDigitCount[guess[i]] = (guessDigitCount[guess[i]] ?? 0) + 1;
      }
    }

    // Calculate cows by taking the minimum count of each digit
    int cows = 0;
    secretDigitCount.forEach((digit, count) {
      if (guessDigitCount.containsKey(digit)) {
        cows += min(count, guessDigitCount[digit]!);
      }
    });

    return cows;
  }
}

class Attempt {
  final String guess;
  final int bulls;
  final int cows;
  final int attemptNumber;

  Attempt({
    required this.guess,
    required this.bulls,
    required this.cows,
    required this.attemptNumber,
  });
}

enum GameStatus { playing, won, lost }

enum GameDifficulty { easy, medium, hard, custom }

// Classe pour représenter un indice
class HintResult {
  final bool available;
  final String digit;
  final bool isBull;
  final int position;

  HintResult({
    required this.available,
    required this.digit,
    required this.isBull,
    required this.position,
  });
}

// Extension pour obtenir les paramètres de jeu en fonction de la difficulté
extension GameDifficultyExtension on GameDifficulty {
  GameModel getGameModel() {
    switch (this) {
      case GameDifficulty.easy:
        return GameModel(
          codeLength: 3,
          maxAttempts: 12,
          allowDuplicates: false,
          maxHints: 3,
        );
      case GameDifficulty.medium:
        return GameModel(
          codeLength: 4,
          maxAttempts: 10,
          allowDuplicates: false,
          maxHints: 2,
        );
      case GameDifficulty.hard:
        return GameModel(
          codeLength: 5,
          maxAttempts: 8,
          allowDuplicates: true,
          maxHints: 1,
        );
      case GameDifficulty.custom:
        return GameModel(); // Utiliser les valeurs par défaut
    }
  }
}
