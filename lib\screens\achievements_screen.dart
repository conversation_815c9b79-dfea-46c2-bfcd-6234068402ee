import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import '../models/achievement_provider.dart';
import '../theme/app_theme.dart';
import '../widgets/achievement_card.dart';
import '../widgets/neon_button.dart';

class AchievementsScreen extends StatelessWidget {
  const AchievementsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('ACHIEVEMENTS'),
        backgroundColor: Colors.transparent,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [AppTheme.neonPurple, AppTheme.neonBlue],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [AppTheme.darkBackground, Color(0xFF121212)],
          ),
        ),
        child: Consumer<AchievementProvider>(
          builder: (context, achievementProvider, _) {
            final unlockedAchievements =
                achievementProvider.unlockedAchievementsList;
            final lockedAchievements =
                achievementProvider.lockedAchievementsList;

            return Column(
              children: [
                // En-tête avec statistiques
                _buildStatsHeader(context, achievementProvider),

                // Liste des succès
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.only(bottom: 16),
                    child: Column(
                      children: [
                        if (unlockedAchievements.isNotEmpty) ...[
                          _buildSectionHeader(
                            context,
                            'Unlocked (${unlockedAchievements.length})',
                          ),
                          ...unlockedAchievements.map(
                            (achievement) => AchievementCard(
                              achievement: achievement,
                              isUnlocked: true,
                              isNew:
                                  achievement.id ==
                                  achievementProvider.newlyUnlocked?.id,
                            ),
                          ),
                        ],

                        if (lockedAchievements.isNotEmpty) ...[
                          _buildSectionHeader(
                            context,
                            'Locked (${lockedAchievements.length})',
                          ),
                          ...lockedAchievements.map(
                            (achievement) => AchievementCard(
                              achievement: achievement,
                              isUnlocked: false,
                            ),
                          ),
                        ],

                        // Espace en bas pour éviter que le dernier élément soit caché
                        const SizedBox(height: 24),
                      ],
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildStatsHeader(BuildContext context, AchievementProvider provider) {
    // Éviter la division par zéro
    final totalAchievements =
        provider.unlockedAchievementsList.length +
        provider.lockedAchievementsList.length;
    final progress =
        totalAchievements > 0
            ? provider.unlockedAchievementsList.length / totalAchievements
            : 0.0;

    // Calculer la largeur disponible
    final availableWidth = MediaQuery.of(context).size.width - 64;
    final progressWidth = availableWidth * progress;

    return Container(
      margin: const EdgeInsets.all(12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: const LinearGradient(
          colors: [Color(0xFF1A1A1A), AppTheme.darkBackgroundSecondary],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: AppTheme.getNeonGlow(AppTheme.neonBlue, intensity: 0.3),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Titre
          Text(
            'Your Progress',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppTheme.neonBlue,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),

          // Barre de progression
          Stack(
            children: [
              // Fond
              Container(
                height: 16,
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.grey.shade800,
                ),
              ),

              // Progression
              AnimatedContainer(
                duration: const Duration(milliseconds: 500),
                height: 16,
                width: progressWidth > 0 ? progressWidth : 0,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  gradient: AppTheme.primaryGradient,
                  boxShadow: AppTheme.getNeonGlow(
                    AppTheme.neonBlue,
                    intensity: 0.5,
                  ),
                ),
              ),

              // Texte de progression
              Center(
                child: Text(
                  '${(progress * 100).toInt()}%',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 10,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Statistiques
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildStatItem(
                context,
                'Games Played',
                '${provider.gamesPlayed}',
                AppTheme.neonBlue,
              ),
              _buildStatItem(
                context,
                'Win Streak',
                '${provider.currentStreak}',
                AppTheme.neonGreen,
              ),
              _buildStatItem(
                context,
                'Best Streak',
                '${provider.maxStreak}',
                AppTheme.neonPink,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(
    BuildContext context,
    String label,
    String value,
    Color color,
  ) {
    return Column(
      children: [
        Text(label, style: TextStyle(color: Colors.white70, fontSize: 12)),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            color: color,
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
      ],
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Text(
        title,
        style: TextStyle(
          color: Colors.white70,
          fontWeight: FontWeight.bold,
          fontSize: 16,
          letterSpacing: 1.2,
        ),
      ),
    );
  }
}
