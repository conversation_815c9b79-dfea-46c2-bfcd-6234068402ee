import 'package:flutter/foundation.dart' show kDebugMode, kIsWeb;
import 'package:audioplayers/audioplayers.dart';

/// Un service audio simplifié qui se concentre uniquement sur la lecture de la musique
class SimpleAudio {
  // Singleton
  static final SimpleAudio _instance = SimpleAudio._internal();

  factory SimpleAudio() {
    return _instance;
  }

  // Constructeur privé
  SimpleAudio._internal() {
    _init();
  }

  // Lecteurs audio
  final AudioPlayer _musicPlayer = AudioPlayer();
  final AudioPlayer _effectsPlayer = AudioPlayer();

  // État
  bool _initialized = false;
  bool _musicEnabled = true;
  bool _soundEnabled = true;

  // Initialisation
  Future<void> _init() async {
    try {
      // Configurer le lecteur de musique
      await _musicPlayer.setReleaseMode(ReleaseMode.loop);
      await _musicPlayer.setVolume(1.0);

      // Configurer le lecteur d'effets sonores
      await _effectsPlayer.setReleaseMode(ReleaseMode.release);
      await _effectsPlayer.setVolume(1.0);

      _initialized = true;

      if (kDebugMode) {
        print('SimpleAudio initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing SimpleAudio: $e');
      }
    }
  }

  // Jouer la musique d'accueil
  Future<void> playHomeMusic() async {
    if (!_musicEnabled) return;

    try {
      if (!_initialized) await _init();

      // Arrêter la musique actuelle
      await _musicPlayer.stop();

      // Jouer la musique d'accueil
      try {
        await _musicPlayer.setSource(
          AssetSource('assets/sounds/home_music.mp3'),
        );
        await _musicPlayer.resume();
        if (kDebugMode) {
          print('Home music started playing with AssetSource');
        }
      } catch (e) {
        if (kDebugMode) {
          print('Error playing home music with AssetSource: $e');
          print('Trying with direct URL...');
        }
        try {
          // Essayer avec une URL directe
          await _musicPlayer.setSourceUrl(
            'http://localhost:5000/bulls_and_cows/lib/assets/sounds/home_music.mp3',
          );
          await _musicPlayer.resume();
          if (kDebugMode) {
            print('Home music started playing with URL');
          }
        } catch (e2) {
          if (kDebugMode) {
            print('Error playing home music with URL: $e2');
          }
        }
      }
      await _musicPlayer.resume();

      if (kDebugMode) {
        print('Home music started playing');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error playing home music: $e');
      }
    }
  }

  // Jouer la musique de jeu
  Future<void> playGameMusic() async {
    if (!_musicEnabled) return;

    try {
      if (!_initialized) await _init();

      // Arrêter la musique actuelle
      await _musicPlayer.stop();

      // Jouer la musique de jeu
      try {
        await _musicPlayer.setSource(
          AssetSource('assets/sounds/game_music.mp3'),
        );
        await _musicPlayer.resume();
        if (kDebugMode) {
          print('Game music started playing with AssetSource');
        }
      } catch (e) {
        if (kDebugMode) {
          print('Error playing game music with AssetSource: $e');
          print('Trying with direct URL...');
        }
        try {
          // Essayer avec une URL directe
          await _musicPlayer.setSourceUrl(
            'http://localhost:5000/bulls_and_cows/lib/assets/sounds/game_music.mp3',
          );
          await _musicPlayer.resume();
          if (kDebugMode) {
            print('Game music started playing with URL');
          }
        } catch (e2) {
          if (kDebugMode) {
            print('Error playing game music with URL: $e2');
          }
        }
      }
      await _musicPlayer.resume();

      if (kDebugMode) {
        print('Game music started playing');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error playing game music: $e');
      }
    }
  }

  // Activer/désactiver la musique
  void toggleMusic() {
    _musicEnabled = !_musicEnabled;

    if (_musicEnabled) {
      _musicPlayer.setVolume(1.0);
    } else {
      _musicPlayer.setVolume(0.0);
    }

    if (kDebugMode) {
      print('Music ${_musicEnabled ? 'enabled' : 'disabled'}');
    }
  }

  // Arrêter la musique
  Future<void> stopMusic() async {
    await _musicPlayer.stop();
  }

  // Jouer un effet sonore
  Future<void> playSound(String soundName) async {
    if (!_soundEnabled) return;

    try {
      if (!_initialized) await _init();

      // Arrêter le son actuel
      await _effectsPlayer.stop();

      // Jouer le son
      try {
        await _effectsPlayer.setSource(
          AssetSource('assets/sounds/$soundName.mp3'),
        );
        await _effectsPlayer.resume();
        if (kDebugMode) {
          print('Sound $soundName started playing with AssetSource');
        }
      } catch (e) {
        if (kDebugMode) {
          print('Error playing sound $soundName with AssetSource: $e');
          print('Trying with direct URL...');
        }
        try {
          // Essayer avec une URL directe
          await _effectsPlayer.setSourceUrl(
            'http://localhost:5000/bulls_and_cows/lib/assets/sounds/$soundName.mp3',
          );
          await _effectsPlayer.resume();
          if (kDebugMode) {
            print('Sound $soundName started playing with URL');
          }
        } catch (e2) {
          if (kDebugMode) {
            print('Error playing sound $soundName with URL: $e2');
          }
        }
      }
      await _effectsPlayer.resume();

      if (kDebugMode) {
        print('Playing sound: $soundName');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error playing sound $soundName: $e');
      }
    }
  }

  // Nettoyer les ressources
  void dispose() {
    _musicPlayer.dispose();
    _effectsPlayer.dispose();
  }
}
