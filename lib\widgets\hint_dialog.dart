import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../models/game_model.dart';
import '../theme/app_theme.dart';
import 'neon_button.dart';

class HintDialog extends StatelessWidget {
  final HintResult hint;
  final int hintsUsed;
  final int maxHints;
  
  const HintDialog({
    Key? key,
    required this.hint,
    required this.hintsUsed,
    required this.maxHints,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(24),
          gradient: const LinearGradient(
            colors: [AppTheme.darkBackground, Color(0xFF1A1A1A)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          boxShadow: AppTheme.getNeonGlow(
            hint.available ? AppTheme.neonGreen : AppTheme.neonPink,
            intensity: 0.5,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'HINT',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: hint.available ? AppTheme.neonGreen : AppTheme.neonPink,
                fontWeight: FontWeight.bold,
                letterSpacing: 2,
              ),
            ),
            const SizedBox(height: 24),
            
            if (hint.available) ...[
              _buildHintContent(context),
            ] else ...[
              _buildNoHintAvailable(context),
            ],
            
            const SizedBox(height: 24),
            
            _buildHintCounter(context),
            
            const SizedBox(height: 24),
            
            NeonButton(
              text: 'OK',
              width: 120,
              height: 50,
              gradient: hint.available ? AppTheme.secondaryGradient : AppTheme.accentGradient,
              onPressed: () => Navigator.pop(context),
            ),
          ],
        ),
      ),
    )
    .animate()
    .scale(
      begin: const Offset(0.8, 0.8),
      end: const Offset(1, 1),
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeOutQuad,
    );
  }
  
  Widget _buildHintContent(BuildContext context) {
    return Column(
      children: [
        Text(
          'The digit',
          style: Theme.of(context).textTheme.bodyLarge,
        ),
        const SizedBox(height: 16),
        
        Container(
          width: 80,
          height: 100,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: AppTheme.primaryGradient,
            boxShadow: AppTheme.getNeonGlow(AppTheme.neonBlue, intensity: 0.7),
          ),
          child: Center(
            child: Text(
              hint.digit,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 48,
              ),
            ),
          ),
        )
        .animate()
        .shimmer(
          duration: const Duration(seconds: 2),
          color: Colors.white.withOpacity(0.5),
        ),
        
        const SizedBox(height: 24),
        
        Text(
          hint.isBull
              ? 'is in the correct position at position ${hint.position + 1}'
              : 'is in the code but at the wrong position',
          textAlign: TextAlign.center,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: hint.isBull ? AppTheme.neonGreen : AppTheme.neonYellow,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }
  
  Widget _buildNoHintAvailable(BuildContext context) {
    return Column(
      children: [
        const Icon(
          Icons.info_outline,
          color: AppTheme.neonPink,
          size: 48,
        ),
        const SizedBox(height: 16),
        
        Text(
          'No hints available',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: AppTheme.neonPink,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        
        Text(
          'You have used all your hints for this game.',
          textAlign: TextAlign.center,
          style: Theme.of(context).textTheme.bodyLarge,
        ),
      ],
    );
  }
  
  Widget _buildHintCounter(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          'Hints used: ',
          style: Theme.of(context).textTheme.bodyLarge,
        ),
        Text(
          '$hintsUsed / $maxHints',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: hintsUsed < maxHints ? AppTheme.neonGreen : AppTheme.neonPink,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }
}
