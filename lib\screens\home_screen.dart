import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import '../models/game_provider.dart';
import '../models/game_model.dart';
import '../models/achievement_provider.dart';
import '../models/achievement_model.dart';
import '../services/simple_audio.dart';
import '../theme/app_theme.dart';
import '../widgets/neon_button.dart';
import '../widgets/achievement_notification.dart';
import 'game_screen.dart';
import 'settings_screen.dart';
import 'achievements_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();

    // Jouer la musique d'accueil immédiatement
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;
      // Utiliser SimpleAudio directement sans Provider
      SimpleAudio().playHomeMusic();

      if (kDebugMode) {
        print('Attempting to play home music immediately after frame render');
      }
    });

    // Vérifier les succès après un court délai
    Future.delayed(const Duration(milliseconds: 500), () {
      if (!mounted) return;
      final achievementProvider = Provider.of<AchievementProvider>(
        context,
        listen: false,
      );
      if (achievementProvider.newlyUnlocked != null) {
        _showAchievementNotification(achievementProvider.newlyUnlocked!);
      }
    });
  }

  @override
  void dispose() {
    // Pas besoin d'arrêter la musique ici car elle continuera à jouer
    // lorsque l'utilisateur navigue vers d'autres écrans
    super.dispose();
  }

  void _showAchievementNotification(Achievement achievement) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) {
        return AchievementNotification(
          achievement: achievement,
          onDismiss: () {
            Navigator.pop(context);
            final achievementProvider = Provider.of<AchievementProvider>(
              context,
              listen: false,
            );
            achievementProvider.clearNewlyUnlocked();
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [AppTheme.darkBackground, Color(0xFF121212)],
          ),
        ),
        child: SafeArea(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Logo et titre
                _buildTitle(context),
                const SizedBox(height: 60),

                // Boutons
                _buildPlayButton(context),
                const SizedBox(height: 24),

                _buildSettingsButton(context),
                const SizedBox(height: 24),

                _buildStatsButton(context),
                const SizedBox(height: 24),

                _buildAchievementsButton(context),
                const SizedBox(height: 60),

                // Statistiques rapides
                _buildQuickStats(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTitle(BuildContext context) {
    return Column(
      children: [
        const Icon(Icons.extension, size: 80, color: AppTheme.neonBlue)
            .animate(onPlay: (controller) => controller.repeat(reverse: true))
            .scale(
              begin: const Offset(1, 1),
              end: const Offset(1.1, 1.1),
              duration: const Duration(seconds: 2),
            )
            .then()
            .rotate(begin: 0, end: 0.05, duration: const Duration(seconds: 1)),

        const SizedBox(height: 24),

        ShaderMask(
              shaderCallback:
                  (bounds) => const LinearGradient(
                    colors: [AppTheme.neonPink, AppTheme.neonBlue],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ).createShader(bounds),
              child: Text(
                'BULLS & COWS',
                style: Theme.of(context).textTheme.displayMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  letterSpacing: 2,
                ),
              ),
            )
            .animate()
            .fadeIn(duration: 800.ms)
            .then()
            .shimmer(duration: 2.seconds, color: Colors.white.withAlpha(204)),
      ],
    );
  }

  Widget _buildPlayButton(BuildContext context) {
    return NeonButton(
      text: 'PLAY',
      width: 250,
      gradient: AppTheme.primaryGradient,
      icon: Icons.play_arrow,
      onPressed: () {
        // Réinitialiser le jeu avant de commencer
        final gameProvider = Provider.of<GameProvider>(context, listen: false);
        gameProvider.restartGame();

        Navigator.push(
          context,
          PageRouteBuilder(
            pageBuilder:
                (context, animation, secondaryAnimation) => const GameScreen(),
            transitionsBuilder: (
              context,
              animation,
              secondaryAnimation,
              child,
            ) {
              return FadeTransition(opacity: animation, child: child);
            },
          ),
        );
      },
    );
  }

  Widget _buildSettingsButton(BuildContext context) {
    return NeonButton(
      text: 'SETTINGS',
      width: 250,
      gradient: AppTheme.secondaryGradient,
      icon: Icons.settings,
      onPressed: () {
        Navigator.push(
          context,
          PageRouteBuilder(
            pageBuilder:
                (context, animation, secondaryAnimation) =>
                    const SettingsScreen(),
            transitionsBuilder: (
              context,
              animation,
              secondaryAnimation,
              child,
            ) {
              return FadeTransition(opacity: animation, child: child);
            },
          ),
        );
      },
    );
  }

  Widget _buildStatsButton(BuildContext context) {
    return NeonButton(
      text: 'STATISTICS',
      width: 250,
      gradient: AppTheme.accentGradient,
      icon: Icons.bar_chart,
      onPressed: () {
        // Afficher les statistiques dans une boîte de dialogue
        showDialog(context: context, builder: (context) => const StatsDialog());
      },
    );
  }

  Widget _buildAchievementsButton(BuildContext context) {
    return NeonButton(
      text: 'ACHIEVEMENTS',
      width: 250,
      gradient: const LinearGradient(
        colors: [AppTheme.neonPurple, AppTheme.neonPink],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
      icon: Icons.emoji_events,
      onPressed: () {
        Navigator.push(
          context,
          PageRouteBuilder(
            pageBuilder:
                (context, animation, secondaryAnimation) =>
                    const AchievementsScreen(),
            transitionsBuilder: (
              context,
              animation,
              secondaryAnimation,
              child,
            ) {
              return FadeTransition(opacity: animation, child: child);
            },
          ),
        );
      },
    );
  }

  Widget _buildQuickStats(BuildContext context) {
    final gameProvider = Provider.of<GameProvider>(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: AppTheme.darkBackgroundSecondary,
        boxShadow: AppTheme.getNeonGlow(AppTheme.neonPurple, intensity: 0.3),
      ),
      child: Column(
        children: [
          Text(
            'QUICK STATS',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppTheme.neonPurple,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildStatItem(
                context,
                'Games',
                '${gameProvider.gamesPlayed}',
                AppTheme.neonBlue,
              ),
              const SizedBox(width: 24),
              _buildStatItem(
                context,
                'Wins',
                '${gameProvider.gamesWon}',
                AppTheme.neonGreen,
              ),
              const SizedBox(width: 24),
              _buildStatItem(
                context,
                'Best',
                gameProvider.bestAttempts == 999
                    ? '-'
                    : '${gameProvider.bestAttempts}',
                AppTheme.neonPink,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(
    BuildContext context,
    String label,
    String value,
    Color color,
  ) {
    return Column(
      children: [
        Text(
          label,
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(color: Colors.white70),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }
}

class StatsDialog extends StatelessWidget {
  const StatsDialog({super.key});

  @override
  Widget build(BuildContext context) {
    final gameProvider = Provider.of<GameProvider>(context);
    final winRate =
        gameProvider.gamesPlayed > 0
            ? (gameProvider.gamesWon / gameProvider.gamesPlayed * 100)
                .toStringAsFixed(1)
            : '0.0';

    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(24),
          gradient: const LinearGradient(
            colors: [AppTheme.darkBackground, Color(0xFF1A1A1A)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          boxShadow: AppTheme.getNeonGlow(AppTheme.neonPurple, intensity: 0.5),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'STATISTICS',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: AppTheme.neonPurple,
                fontWeight: FontWeight.bold,
                letterSpacing: 2,
              ),
            ),
            const SizedBox(height: 24),

            _buildStatRow(
              context,
              'Games Played',
              '${gameProvider.gamesPlayed}',
            ),
            _buildStatRow(context, 'Games Won', '${gameProvider.gamesWon}'),
            _buildStatRow(context, 'Win Rate', '$winRate%'),
            _buildStatRow(
              context,
              'Best Score',
              gameProvider.bestAttempts == 999
                  ? '-'
                  : '${gameProvider.bestAttempts} attempts',
            ),
            _buildStatRow(
              context,
              'Current Difficulty',
              _getDifficultyName(gameProvider.difficulty),
            ),

            const SizedBox(height: 24),

            NeonButton(
              text: 'RESET STATS',
              width: 200,
              height: 50,
              gradient: AppTheme.accentGradient,
              onPressed: () {
                gameProvider.resetStats();
                Navigator.pop(context);
              },
            ),

            const SizedBox(height: 16),

            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'CLOSE',
                style: TextStyle(
                  color: AppTheme.neonBlue,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    ).animate().scale(
      begin: const Offset(0.8, 0.8),
      end: const Offset(1, 1),
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeOutQuad,
    );
  }

  Widget _buildStatRow(BuildContext context, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(color: Colors.white70),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  String _getDifficultyName(GameDifficulty difficulty) {
    switch (difficulty) {
      case GameDifficulty.easy:
        return 'Easy';
      case GameDifficulty.medium:
        return 'Medium';
      case GameDifficulty.hard:
        return 'Hard';
      case GameDifficulty.custom:
        return 'Custom';
    }
  }
}
