import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../theme/app_theme.dart';

class NeonKeypad extends StatelessWidget {
  final Function(String) onDigitPressed;
  final VoidCallback onDeletePressed;
  final VoidCallback onSubmitPressed;
  final bool isSubmitEnabled;
  final Set<String> usedDigits;

  const NeonKeypad({
    super.key,
    required this.onDigitPressed,
    required this.onDeletePressed,
    required this.onSubmitPressed,
    this.isSubmitEnabled = false,
    this.usedDigits = const {},
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.darkBackgroundSecondary,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
        boxShadow: [
          BoxShadow(
            color: AppTheme.neonBlue.withOpacity(0.1),
            blurRadius: 20,
            spreadRadius: 5,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Ligne 1: 1, 2, 3
          _buildKeypadRow(['1', '2', '3']),
          const SizedBox(height: 16),

          // Ligne 2: 4, 5, 6
          _buildKeypadRow(['4', '5', '6']),
          const SizedBox(height: 16),

          // Ligne 3: 7, 8, 9
          _buildKeypadRow(['7', '8', '9']),
          const SizedBox(height: 16),

          // Ligne 4: Delete, 0, Submit
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildActionKey(
                icon: Icons.backspace,
                onPressed: onDeletePressed,
                gradient: AppTheme.accentGradient,
              ),
              _buildDigitKey('0'),
              _buildActionKey(
                icon: Icons.check,
                onPressed: isSubmitEnabled ? onSubmitPressed : null,
                gradient:
                    isSubmitEnabled
                        ? AppTheme.secondaryGradient
                        : const LinearGradient(
                          colors: [Colors.grey, Colors.grey],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildKeypadRow(List<String> digits) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: digits.map((digit) => _buildDigitKey(digit)).toList(),
    );
  }

  Widget _buildDigitKey(String digit) {
    final isUsed = usedDigits.contains(digit);

    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculer la taille en fonction de la largeur de l'écran
        final screenWidth = MediaQuery.of(context).size.width;
        final keySize = screenWidth < 360 ? 60.0 : 70.0;
        final fontSize = screenWidth < 360 ? 24.0 : 28.0;

        return GestureDetector(
          onTap: isUsed ? null : () => onDigitPressed(digit),
          child: Container(
                width: keySize,
                height: keySize,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient:
                      isUsed
                          ? const LinearGradient(
                            colors: [Colors.grey, Colors.grey],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          )
                          : AppTheme.primaryGradient,
                  boxShadow:
                      isUsed
                          ? []
                          : AppTheme.getNeonGlow(
                            AppTheme.neonBlue,
                            intensity: 0.5,
                          ),
                ),
                child: Center(
                  child: Text(
                    digit,
                    style: TextStyle(
                      color: isUsed ? Colors.grey.shade300 : Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: fontSize,
                    ),
                  ),
                ),
              )
              .animate(onPlay: (controller) => controller.repeat(reverse: true))
              .scale(
                begin: const Offset(1, 1),
                end: const Offset(1.05, 1.05),
                duration: const Duration(seconds: 1),
              ),
        );
      },
    );
  }

  Widget _buildActionKey({
    required IconData icon,
    required VoidCallback? onPressed,
    required LinearGradient gradient,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculer la taille en fonction de la largeur de l'écran
        final screenWidth = MediaQuery.of(context).size.width;
        final keySize = screenWidth < 360 ? 60.0 : 70.0;
        final iconSize = screenWidth < 360 ? 24.0 : 28.0;

        return GestureDetector(
          onTap: onPressed,
          child: Container(
                width: keySize,
                height: keySize,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: gradient,
                  boxShadow:
                      onPressed == null
                          ? []
                          : AppTheme.getNeonGlow(
                            gradient.colors.first,
                            intensity: 0.5,
                          ),
                ),
                child: Center(
                  child: Icon(icon, color: Colors.white, size: iconSize),
                ),
              )
              .animate(onPlay: (controller) => controller.repeat(reverse: true))
              .scale(
                begin: const Offset(1, 1),
                end: const Offset(1.05, 1.05),
                duration: const Duration(seconds: 1),
              ),
        );
      },
    );
  }
}
