import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'achievement_model.dart';
import 'game_model.dart';

class AchievementProvider extends ChangeNotifier {
  // Statistiques pour les succès
  int _gamesPlayed = 0;
  int _gamesWon = 0;
  int _bestAttempts = 999;
  int _currentStreak = 0;
  int _maxStreak = 0;
  int _totalHintsUsed = 0;
  int _perfectGames = 0; // Victoires en 4 essais ou moins
  int _oneAttemptWins = 0; // Victoires en 1 essai
  int _persistentWins = 0; // Victoires en 8 essais ou plus
  int _fastGames = 0; // Victoires en moins de 30 secondes
  
  // Temps de début de partie pour calculer la durée
  DateTime? _gameStartTime;
  
  // Liste des IDs des succès débloqués
  Set<String> _unlockedAchievements = {};
  
  // Succès nouvellement débloqué à afficher
  Achievement? _newlyUnlocked;
  
  // Getters
  int get gamesPlayed => _gamesPlayed;
  int get gamesWon => _gamesWon;
  int get bestAttempts => _bestAttempts;
  int get currentStreak => _currentStreak;
  int get maxStreak => _maxStreak;
  int get totalHintsUsed => _totalHintsUsed;
  int get perfectGames => _perfectGames;
  int get oneAttemptWins => _oneAttemptWins;
  int get persistentWins => _persistentWins;
  int get fastGames => _fastGames;
  Set<String> get unlockedAchievements => _unlockedAchievements;
  Achievement? get newlyUnlocked => _newlyUnlocked;
  
  // Liste des succès débloqués
  List<Achievement> get unlockedAchievementsList {
    return Achievements.all
        .where((achievement) => _unlockedAchievements.contains(achievement.id))
        .toList();
  }
  
  // Liste des succès verrouillés (non secrets)
  List<Achievement> get lockedAchievementsList {
    return Achievements.all
        .where((achievement) => 
            !_unlockedAchievements.contains(achievement.id) && 
            !achievement.isSecret)
        .toList();
  }
  
  AchievementProvider() {
    _loadStats();
  }
  
  // Méthode pour démarrer une nouvelle partie
  void startGame() {
    _gameStartTime = DateTime.now();
  }
  
  // Méthode pour mettre à jour les statistiques après une partie
  void updateStats({
    required GameStatus status,
    required int attempts,
    required int hintsUsed,
    required bool isNewGame,
  }) {
    if (isNewGame) {
      if (status == GameStatus.won) {
        _gamesPlayed++;
        _gamesWon++;
        _currentStreak++;
        
        if (_currentStreak > _maxStreak) {
          _maxStreak = _currentStreak;
        }
        
        if (attempts < _bestAttempts) {
          _bestAttempts = attempts;
        }
        
        _totalHintsUsed += hintsUsed;
        
        // Vérifier les conditions spécifiques
        if (attempts <= 4) {
          _perfectGames++;
        }
        
        if (attempts == 1) {
          _oneAttemptWins++;
        }
        
        if (attempts >= 8) {
          _persistentWins++;
        }
        
        // Vérifier si c'est une victoire rapide
        if (_gameStartTime != null) {
          final duration = DateTime.now().difference(_gameStartTime!);
          if (duration.inSeconds < 30) {
            _fastGames++;
          }
        }
      } else if (status == GameStatus.lost) {
        _gamesPlayed++;
        _currentStreak = 0;
        _totalHintsUsed += hintsUsed;
      }
      
      _saveStats();
      _checkAchievements();
    }
    
    // Réinitialiser le temps de début pour la prochaine partie
    _gameStartTime = null;
  }
  
  // Méthode pour vérifier les succès débloqués
  void _checkAchievements() {
    _newlyUnlocked = null;
    
    for (final achievement in Achievements.all) {
      if (!_unlockedAchievements.contains(achievement.id)) {
        final isUnlocked = achievement.isUnlocked(
          gamesPlayed: _gamesPlayed,
          gamesWon: _gamesWon,
          bestAttempts: _bestAttempts,
          currentStreak: _currentStreak,
          maxStreak: _maxStreak,
          totalHintsUsed: _totalHintsUsed,
          perfectGames: _perfectGames,
          oneAttemptWins: _oneAttemptWins,
          persistentWins: _persistentWins,
          fastGames: _fastGames,
        );
        
        if (isUnlocked) {
          _unlockedAchievements.add(achievement.id);
          _newlyUnlocked = achievement;
          _saveUnlockedAchievements();
          notifyListeners();
          
          // Ne notifier que du premier succès débloqué à la fois
          break;
        }
      }
    }
  }
  
  // Méthode pour effacer le succès nouvellement débloqué après l'avoir affiché
  void clearNewlyUnlocked() {
    _newlyUnlocked = null;
    notifyListeners();
    
    // Vérifier s'il y a d'autres succès débloqués
    _checkAchievements();
  }
  
  // Méthode pour réinitialiser les statistiques
  void resetStats() {
    _gamesPlayed = 0;
    _gamesWon = 0;
    _bestAttempts = 999;
    _currentStreak = 0;
    _maxStreak = 0;
    _totalHintsUsed = 0;
    _perfectGames = 0;
    _oneAttemptWins = 0;
    _persistentWins = 0;
    _fastGames = 0;
    _unlockedAchievements = {};
    _newlyUnlocked = null;
    
    _saveStats();
    _saveUnlockedAchievements();
    notifyListeners();
  }
  
  // Méthodes pour charger/sauvegarder les statistiques
  Future<void> _loadStats() async {
    final prefs = await SharedPreferences.getInstance();
    
    _gamesPlayed = prefs.getInt('achievement_gamesPlayed') ?? 0;
    _gamesWon = prefs.getInt('achievement_gamesWon') ?? 0;
    _bestAttempts = prefs.getInt('achievement_bestAttempts') ?? 999;
    _currentStreak = prefs.getInt('achievement_currentStreak') ?? 0;
    _maxStreak = prefs.getInt('achievement_maxStreak') ?? 0;
    _totalHintsUsed = prefs.getInt('achievement_totalHintsUsed') ?? 0;
    _perfectGames = prefs.getInt('achievement_perfectGames') ?? 0;
    _oneAttemptWins = prefs.getInt('achievement_oneAttemptWins') ?? 0;
    _persistentWins = prefs.getInt('achievement_persistentWins') ?? 0;
    _fastGames = prefs.getInt('achievement_fastGames') ?? 0;
    
    final unlockedList = prefs.getStringList('unlockedAchievements') ?? [];
    _unlockedAchievements = Set<String>.from(unlockedList);
    
    notifyListeners();
  }
  
  Future<void> _saveStats() async {
    final prefs = await SharedPreferences.getInstance();
    
    await prefs.setInt('achievement_gamesPlayed', _gamesPlayed);
    await prefs.setInt('achievement_gamesWon', _gamesWon);
    await prefs.setInt('achievement_bestAttempts', _bestAttempts);
    await prefs.setInt('achievement_currentStreak', _currentStreak);
    await prefs.setInt('achievement_maxStreak', _maxStreak);
    await prefs.setInt('achievement_totalHintsUsed', _totalHintsUsed);
    await prefs.setInt('achievement_perfectGames', _perfectGames);
    await prefs.setInt('achievement_oneAttemptWins', _oneAttemptWins);
    await prefs.setInt('achievement_persistentWins', _persistentWins);
    await prefs.setInt('achievement_fastGames', _fastGames);
  }
  
  Future<void> _saveUnlockedAchievements() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setStringList('unlockedAchievements', _unlockedAchievements.toList());
  }
}
